import { Col, Row, Button } from "reactstrap";
import { useTranslation } from "react-i18next";
import CustomInput from "../Common/Input";
import { FaUndo, FaTimes } from "react-icons/fa";
import CustomFilterSearch from "../Common/CustomFilterSearch";
import { useState } from "react";
import { IoIosSearch } from "react-icons/io";
import { RiResetRightFill } from "react-icons/ri";

const SearchCard = ({
  SearchData,
  control,
  inputsArray,
  handelSearch,
  customComponents,
  hadelReset,
  watch,
  setValue,
  hasSearch = false,
  customItems,
}) => {
  const { t } = useTranslation();

  const formValues = watch();
  const selectedValues = SearchData.map((item) => formValues[item.name]);

  const [isSearched, setIsSearched] = useState(false);
  const onSearchClick = () => {
    setIsSearched(true);
    handelSearch();
  };

  const onResetClick = () => {
    setIsSearched(false);
    hadelReset();
  };
  return (
    <div
      className="w-100 bg-white"
      style={{
        borderTopLeftRadius: 5,
        borderTopRightRadius: 5,
        // height: "fitContent",
      }}
    >
      <div className="d-flex align-items-center justify-content-between">
        <h5>{t("common.filter_by")}</h5>

        <Col xs="auto" className="mb-2 ms-auto d-flex gap-2">
          <Button
            color="light"
            size="sm"
            className="d-inline-flex align-items-center gap-1 border"
            onClick={onResetClick}
            style={{
              padding: "0.375rem 0.75rem",
              color: "#6c757d",
              whiteSpace: "nowrap",
              width: "fit-content",
            }}
            title={t("common.reset")}
          >
            <FaUndo style={{ fontSize: "0.75rem" }} />
          </Button>
          <Button
            color="primary"
            size="sm"
            className="d-inline-flex align-items-center gap-1 border"
            onClick={onSearchClick}
            title={t("common.reset")}
          >
            {/* <i
              className="ri-search-line"
              style={{ fontSize: "12px", paddingInline: "4px" }}
            /> */}
            <IoIosSearch size={18} />
          </Button>
        </Col>
      </div>
      <div>
        {hasSearch && (
          <Col xs={12} className="mb-2">
            <CustomInput
              name="search"
              control={control}
              label="Search"
              startIcon={
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <RiResetRightFill size={18} />
                </div>
              }
            />
          </Col>
        )}
        <form>
          <Row className="g-1 position-relative align-items-end">
            {inputsArray.map((item) => (
              <Col key={item.id} xs={12} sm={6} md={4} lg={3} className="mb-2">
                <CustomInput
                  name={item.name}
                  control={control}
                  label={item.label}
                  type={item.type}
                  inputStyle={{ background: "#fff" }}
                />
              </Col>
            ))}

            {SearchData.map((item) => {
              const SelectComponent = item.component || CustomFilterSearch;
              return (
                <Col
                  key={item.id}
                  xs={12}
                  sm={6}
                  md={4}
                  lg={3}
                  className="mb-2"
                >
                  <SelectComponent
                    name={item.name}
                    control={control}
                    options={item.options}
                    label={item.label}
                    watch={watch}
                    setValue={setValue}
                    isMulti={item.isMulti}
                  />
                </Col>
              );
            })}

            {customComponents && customComponents}
          </Row>
        </form>
        <div className="d-flex align-items-center gap-1">
          {selectedValues.some((value) => value) && (
            <div className="d-flex flex-wrap gap-2 mb-3 mt-2">
              {SearchData.map((item) => {
                const value = formValues[item.name];
                if (!value) return null;

                // Handle multi-select values (arrays)
                if (Array.isArray(value) && value.length > 0) {
                  return (
                    <div key={item.id} className="d-flex flex-wrap gap-1">
                      {/* Header badge with count */}
                      <div
                        className="badge border d-flex g-2 align-items-center px-3 py-2"
                        style={{
                          background: "#0d6efd",
                          color: "#fff",
                        }}
                      >
                        <span className="me-1">
                          {item.label}: ({value.length})
                        </span>
                      </div>

                      {/* Individual badges for each selected item */}
                      {value.map((v, index) => (
                        <div
                          key={`${item.id}-${index}`}
                          className="badge border d-flex g-2 align-items-center px-3 py-2"
                          style={{
                            background: "#5c8fdd",
                            color: "#fff",
                          }}
                        >
                          <span className="me-2">{v.label}</span>
                          <FaTimes
                            role="button"
                            style={{
                              fontSize: 10,
                              color: "#fff",
                            }}
                            onClick={() => {
                              // Remove only this specific item
                              const newValue = [...value];
                              newValue.splice(index, 1);
                              setValue(
                                item.name,
                                newValue.length ? newValue : null
                              );
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  );
                }

                // Handle single value (non-array)
                return (
                  <div
                    key={item.id}
                    className="badge border d-flex g-2 align-items-center px-3 py-2"
                    style={{
                      background: "#0d6efd",
                      color: "#fff",
                    }}
                  >
                    <span className="me-3">
                      {item.label}: {value?.label || value}
                    </span>
                    <FaTimes
                      role="button"
                      style={{
                        fontSize: 12,
                        color: "#fff",
                      }}
                      onClick={() => {
                        setValue(item.name, null);
                      }}
                    />
                  </div>
                );
              })}
            </div>
          )}
          <div className="d-flex flex-wrap gap-2 mb-3 mt-2">
            {isSearched &&
              inputsArray.map((item) => {
                const value = formValues[item.name];
                if (!value) return null;

                return (
                  <div
                    key={item.id}
                    className="badge border d-flex g-2 align-items-center px-3 py-2"
                    style={{
                      background: "#0d6efd",
                      color: "#fff",
                    }}
                  >
                    <span className="me-2">{item.label}:</span>
                    <span className="me-2">{value}</span>
                    <FaTimes
                      role="button"
                      style={{
                        fontSize: 12,
                        color: "#fff",
                      }}
                      onClick={() => {
                        setValue(item.name, ""); // or null if needed
                      }}
                    />
                  </div>
                );
              })}
            {customItems}
          </div>
        </div>
      </div>
    </div>
  );
};
export default SearchCard;
